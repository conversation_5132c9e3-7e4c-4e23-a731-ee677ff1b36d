// User types
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

export interface User {
  id: number;
  username: string;
  password?: string;
  isAdmin?: boolean; // Changed from role to isAdmin
  name?: string;
  lastName?: string;
  profileImage?: string;
  createdAt: string;
  updatedAt: string;
  rank?: number;
}

export interface LoginUser {
  id: number;
  username: string;
  isAdmin?: boolean; // Changed from role to isAdmin
  role?: UserRole; // Add role property
  name?: string;
  lastName?: string;
  profileImage?: string;
}

export interface LoginResponse {
  access_token: string;
  user: LoginUser;
}

export interface LoginDto {
  username: string;
  password: string;
}

export interface RegisterDto {
  username: string;
  password: string;
  isAdmin?: boolean; // Changed from role to isAdmin
  name?: string;
  lastName?: string;
  profileImage?: string;
}

// Course Group types
export interface CourseGroup {
  id: number;
  name: string;
  positiveCoefficient: number;
  courses?: Course[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateCourseGroupDto {
  name: string;
  positiveCoefficient?: number;
}

// Course types
export interface Course {
  id: number;
  name: string;
  courseGroupId: number;
  courseGroup?: CourseGroup;
  positiveCoefficient: number;
  negativeCoefficient: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCourseDto {
  name: string;
  courseGroupId: number;
  positiveCoefficient: number;
  negativeCoefficient: number;
}

// Exam types
export interface ExamCourse {
  courseId: number;
  questionCount: number;
}

export interface Exam {
  id: number;
  name: string;
  courses: ExamCourse[];
  sessions?: Session[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateExamDto {
  name: string;
  courses: ExamCourse[];
}

// Question types
export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
}

export enum AnswerOption {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

export interface Question {
  id: number;
  examId: number;
  courseId: number;
  questionNumber: number;
  questionText: string;
  options: string[];
  correctAnswer: AnswerOption;
  type: QuestionType;
  course?: Course;
  exam?: Exam;
  createdAt: string;
  updatedAt: string;
}

export interface CreateQuestionDto {
  examId: number;
  courseId: number;
  questionNumber: number;
  questionText: string;
  options: string[];
  correctAnswer: AnswerOption;
  type?: QuestionType;
}

// Session types
export interface SessionAnswer {
  courseId: number;
  correctAnswers: number;
  wrongAnswers: number;
  unanswered: number;
}

export interface QuestionAnswer {
  questionId: number;
  questionNumber: number;
  courseId: number;
  selectedAnswer: string | null;
  isCorrect: boolean;
}

export interface CourseScore {
  courseId: number;
  courseName: string;
  percentage: number;
  correctAnswers: number;
  wrongAnswers: number;
  unanswered: number;
  totalQuestions: number;
}

export interface CourseGroupScore {
  courseGroupId: number;
  courseGroupName: string;
  averageScore: number;
  weightedScore: number;
  courses: CourseScore[];
}

export interface ScoreDetails {
  courseScores: CourseScore[];
  courseGroupScores: CourseGroupScore[];
  answers?: SessionAnswer[];
  questionAnswers?: QuestionAnswer[];
}

export interface Session {
  id: number;
  examId: number;
  userId: number;
  exam?: Exam;
  user?: User;
  totalScore: number;
  scoreDetails: ScoreDetails;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSessionDto {
  examId: number;
  answers: SessionAnswer[];
}

export interface QuestionAnswerDto {
  questionId: number;
  selectedAnswer?: string;
}

export interface CreateSessionByQuestionsDto {
  examId: number;
  questionAnswers: QuestionAnswerDto[];
}

// API Response types
export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}
