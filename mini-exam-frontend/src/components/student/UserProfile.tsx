import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { User } from '../../types';
import apiService from '../../services/api';
import ImageUpload from '../common/ImageUpload';

const UserProfile: React.FC = () => {
  const { user: authUser, refreshUser } = useAuth();
  const [formData, setFormData] = useState<Partial<User>>({
    username: '',
    name: '',
    lastName: '',
    profileImage: '',
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const profile = await apiService.getProfile();
        console.log('📋 Profile data received:', profile);
        setFormData({
          username: profile.username,
          name: profile.name,
          lastName: profile.lastName,
          profileImage: profile.profileImage || '',
        });
        console.log('🖼️ Profile image value:', profile.profileImage);
      } catch (err) {
        setError('خطا در بارگذاری اطلاعات پروفایل');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleImageUpload = async (imageUrl: string) => {
    console.log('🎯 UserProfile: handleImageUpload called with:', imageUrl);
    try {
      if (authUser?.id) {
        console.log('👤 User ID:', authUser.id);
        // The image is already uploaded by the ImageUpload component
        // We just need to update the local state and refresh the auth context
        setFormData((prev) => ({ ...prev, profileImage: imageUrl }));
        setSuccess('تصویر پروفایل با موفقیت آپلود شد!');

        // Refresh the user data in auth context
        console.log('🔄 Refreshing user data...');
        await refreshUser();
        console.log('✅ User data refreshed');
      } else {
        console.error('❌ No authUser.id available');
      }
    } catch (err) {
      console.error('❌ Error in handleImageUpload:', err);
      setError('خطا در آپلود تصویر پروفایل');
    }
  };

  const handleImageError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!authUser?.id) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const dataToSend = {
        name: formData.name,
        lastName: formData.lastName,
      };
      
      await apiService.updateUser(authUser.id, dataToSend);
      setSuccess('اطلاعات پروفایل با موفقیت به‌روزرسانی شد!');
      setIsEditing(false);
      
      // Refresh the user data in auth context
      await refreshUser();
    } catch (err) {
      setError('خطا در به‌روزرسانی اطلاعات پروفایل');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    if (authUser) {
      setFormData({
        username: authUser.username,
        name: authUser.name,
        lastName: authUser.lastName,
        profileImage: authUser.profileImage || '',
      });
    }
    setIsEditing(false);
    setError(null);
    setSuccess(null);
  };

  if (loading && !formData.username) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        <p className="mt-4 text-dark-300">در حال بارگذاری اطلاعات پروفایل...</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">پروفایل کاربری</h1>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              ویرایش پروفایل
            </button>
          )}
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          {/* Profile Image Section */}
          <div className="mb-6">
            <label className="block text-dark-300 text-sm font-bold mb-2">
              تصویر پروفایل:
            </label>
            <ImageUpload
              userId={authUser?.id}
              currentImageUrl={formData.profileImage ? (
                formData.profileImage.startsWith('http')
                  ? formData.profileImage
                  : `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}/uploads/profile-images/${formData.profileImage}`
              ) : undefined}
              onImageUploaded={handleImageUpload}
              onError={handleImageError}
              disabled={loading}
              className="max-w-sm"
            />
          </div>

          {/* Username (Read-only) */}
          <div className="mb-4">
            <label className="block text-dark-300 text-sm font-bold mb-2">
              نام کاربری:
            </label>
            <input
              type="text"
              value={formData.username || ''}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-white cursor-not-allowed"
              disabled
            />
            <p className="text-xs text-dark-400 mt-1">نام کاربری قابل تغییر نیست</p>
          </div>

          {/* Name */}
          <div className="mb-4">
            <label className="block text-dark-300 text-sm font-bold mb-2">
              نام:
            </label>
            <input
              type="text"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              disabled={!isEditing || loading}
              className={`w-full px-3 py-2 border rounded-md text-white ${
                isEditing && !loading
                  ? 'bg-dark-700 border-dark-600 focus:border-primary-500 focus:outline-none'
                  : 'bg-dark-800 border-dark-700 cursor-not-allowed'
              }`}
            />
          </div>

          {/* Last Name */}
          <div className="mb-6">
            <label className="block text-dark-300 text-sm font-bold mb-2">
              نام خانوادگی:
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName || ''}
              onChange={handleChange}
              disabled={!isEditing || loading}
              className={`w-full px-3 py-2 border rounded-md text-white ${
                isEditing && !loading
                  ? 'bg-dark-700 border-dark-600 focus:border-primary-500 focus:outline-none'
                  : 'bg-dark-800 border-dark-700 cursor-not-allowed'
              }`}
            />
          </div>

          {/* Action Buttons */}
          {isEditing && (
            <div className="flex space-x-4 space-x-reverse">
              <button
                type="submit"
                disabled={loading}
                className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-6 py-2 rounded-md font-medium"
              >
                {loading ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                disabled={loading}
                className="bg-dark-600 hover:bg-dark-500 disabled:opacity-50 text-white px-6 py-2 rounded-md font-medium"
              >
                انصراف
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default UserProfile;
