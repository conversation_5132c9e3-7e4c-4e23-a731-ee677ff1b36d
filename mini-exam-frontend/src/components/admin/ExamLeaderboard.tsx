import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Session, Exam } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

interface ExamStatistics {
  examId: number;
  totalSessions: number;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  scoreDistribution: Array<{
    range: string;
    count: number;
  }>;
}

const ExamLeaderboard: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [exam, setExam] = useState<Exam | null>(null);
  const [statistics, setStatistics] = useState<ExamStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      if (!examId) {
        setError('شناسه آزمون نامعتبر است');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const [sessionsData, examData, statisticsData] = await Promise.all([
          apiService.getSessionsByExam(parseInt(examId)),
          apiService.getExam(parseInt(examId)),
          apiService.getExamStatistics(parseInt(examId)),
        ]);

        setSessions(sessionsData);
        setExam(examData);
        setStatistics(statisticsData);
      } catch (err: any) {
        setError('خطا در بارگذاری اطلاعات');
        console.error('Error fetching leaderboard data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [examId]);

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'text-yellow-400'; // Gold
      case 2:
        return 'text-gray-300'; // Silver
      case 3:
        return 'text-orange-400'; // Bronze
      default:
        return 'text-dark-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (error || !exam) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error || 'آزمون یافت نشد'}</p>
        <button
          onClick={() => navigate('/admin/exams')}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded"
        >
          بازگشت به لیست آزمون‌ها
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-white">
          نتایج آزمون: {exam.name}
        </h1>
        <button
          onClick={() => navigate("/admin/exams")}
          className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
        >
          بازگشت به آزمون‌ها
        </button>
      </div>

      {/* Statistics Overview */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-dark-800 p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              تعداد شرکت‌کنندگان
            </h3>
            <p className="text-3xl font-bold text-primary-400">
              {convertDigitsToPersian(statistics.totalSessions)}
            </p>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              میانگین نمرات
            </h3>
            <p className="text-3xl font-bold text-blue-400">
              {convertDigitsToPersian(statistics.averageScore?.toFixed(1))}
            </p>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              بالاترین نمره
            </h3>
            <p className="text-3xl font-bold text-green-400">
              {convertDigitsToPersian(statistics.highestScore?.toFixed(1))}
            </p>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              پایین‌ترین نمره
            </h3>
            <p className="text-3xl font-bold text-red-400">
              {convertDigitsToPersian(statistics.lowestScore?.toFixed(1))}
            </p>
          </div>
        </div>
      )}

      {/* Leaderboard Table */}
      <div className="bg-dark-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-dark-700">
          <h2 className="text-xl font-semibold text-white">جدول امتیازات</h2>
        </div>

        {sessions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">
              هیچ نتیجه‌ای برای این آزمون ثبت نشده است
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                    رتبه
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                    نام دانش‌آموز
                  </th>
                  {/* <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                    نام کاربری
                  </th> */}
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    نمره کل
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    تاریخ آزمون
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    عملیات
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    مشاهده نتیجه
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {sessions.map((session, index) => (
                  <tr key={session.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span
                          className={`text-2xl font-bold ${getRankColor(
                            index + 1
                          )}`}
                        >
                          {convertDigitsToPersian(index + 1)}
                        </span>
                        {index < 3 && (
                          <span className="mr-2 text-lg">
                            {index === 0 ? "🥇" : index === 1 ? "🥈" : "🥉"}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-white font-medium">
                        {session.user?.name && session.user?.lastName
                          ? `${session.user.name} ${session.user.lastName}`
                          : "نام نامشخص"}
                      </div>
                    </td>
                    {/* <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-dark-300">
                        {session.user?.username || "نامشخص"}
                      </div>
                    </td> */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span
                        className={`text-xl font-bold ${getScoreColor(
                          session.totalScore
                        )}`}
                      >
                        {convertDigitsToPersian(session.totalScore)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center text-dark-300">
                      {convertDigitsToPersian(
                        new Date(session.createdAt).toLocaleDateString("fa-IR")
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() =>
                          navigate(`/admin/session-result/${session.id}`)
                        }
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                      >
                        جزئیات
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => navigate(`/admin/session-result/${session.id}`)}
                        className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm"
                      >
                        مشاهده
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Score Distribution Chart */}
      {statistics && statistics.scoreDistribution.length > 0 && (
        <div className="mt-8 bg-dark-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">توزیع نمرات</h3>
          <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
            {statistics.scoreDistribution.map((range, index) => (
              <div key={index} className="text-center">
                <div
                  className="bg-primary-600 rounded-t"
                  style={{
                    height: `${Math.max(
                      (range.count / statistics.totalSessions) * 100,
                      5
                    )}px`,
                    minHeight: "20px",
                  }}
                ></div>
                <div className="text-xs text-dark-300 mt-1">
                  {convertDigitsToPersian(range.range)}
                </div>
                <div className="text-xs text-dark-400">
                  ({convertDigitsToPersian(range.count)})
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamLeaderboard;
