CREATE TYPE questions_correct_answer_enum AS ENUM ('A', 'B', 'C', 'D');
CREATE TYPE questions_type_enum AS ENUM ('multiple_choice');
CREATE TYPE users_role_enum AS ENUM ('admin', 'user');
-- Adminer 5.3.0 PostgreSQL 16.9 dump

DROP TABLE IF EXISTS "course_groups";
DROP SEQUENCE IF EXISTS course_groups_id_seq;
CREATE SEQUENCE course_groups_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."course_groups" (
    "id" integer DEFAULT nextval('course_groups_id_seq') NOT NULL,
    "name" character varying NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    "positive_coefficient" double precision DEFAULT '1' NOT NULL,
    CONSTRAINT "PK_9722c03add9ea0dca5c69447398" PRIMARY KEY ("id")
)
WITH (oids = false);

INSERT INTO "course_groups" ("id", "name", "createdAt", "updatedAt", "positive_coefficient") VALUES
(1,	'دروس عمومی',	'2025-07-31 18:42:09.578504',	'2025-07-31 18:42:09.578504',	3),
(4,	'دروس شایستگی غیر فنی',	'2025-07-31 18:42:09.578504',	'2025-07-31 18:42:09.578504',	3),
(3,	'دروس شایستگی پایه',	'2025-07-31 18:42:09.578504',	'2025-07-31 18:42:09.578504',	6),
(5,	'دروس شایستگی فنی',	'2025-07-31 18:42:09.578504',	'2025-07-31 18:42:09.578504',	12),
(2,	'ریاضی و فیزیک',	'2025-07-31 18:42:09.578504',	'2025-07-31 21:14:31.706212',	1);

DROP TABLE IF EXISTS "courses";
DROP SEQUENCE IF EXISTS courses_id_seq;
CREATE SEQUENCE courses_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."courses" (
    "id" integer DEFAULT nextval('courses_id_seq') NOT NULL,
    "name" character varying NOT NULL,
    "course_group_id" integer NOT NULL,
    "positive_coefficient" integer NOT NULL,
    "negative_coefficient" integer NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "PK_3f70a487cc718ad8eda4e6d58c9" PRIMARY KEY ("id")
)
WITH (oids = false);

INSERT INTO "courses" ("id", "name", "course_group_id", "positive_coefficient", "negative_coefficient", "createdAt", "updatedAt") VALUES
(3,	'عربی ۳',	1,	3,	1,	'2025-07-28 06:47:39.701328',	'2025-07-28 06:47:39.701328'),
(4,	'دین و زندگی ۳',	1,	3,	1,	'2025-07-28 06:48:01.942962',	'2025-07-28 06:48:01.942962'),
(5,	'زبان انگلیسی',	1,	3,	1,	'2025-07-28 06:48:14.340761',	'2025-07-28 06:48:14.340761'),
(6,	'ریاضی ۱',	3,	3,	1,	'2025-07-28 06:57:45.984346',	'2025-07-28 06:57:45.984346'),
(7,	'ریاضی ۲',	3,	3,	1,	'2025-07-28 06:57:58.854277',	'2025-07-28 06:57:58.854277'),
(8,	'ریاضی ۳',	3,	3,	1,	'2025-07-28 06:58:19.087422',	'2025-07-28 06:58:19.087422'),
(9,	'شیمی',	3,	3,	1,	'2025-07-28 06:59:24.375455',	'2025-07-28 06:59:24.375455'),
(10,	'فیزیک',	3,	3,	1,	'2025-07-28 06:59:33.234122',	'2025-07-28 06:59:33.234122'),
(11,	'الزامات محیط کار',	4,	3,	1,	'2025-07-28 06:59:59.682938',	'2025-07-28 06:59:59.682938'),
(12,	'فناوری نوین',	4,	3,	1,	'2025-07-28 07:00:10.513335',	'2025-07-28 07:00:10.513335'),
(13,	'کارگاه نوآوری',	4,	3,	1,	'2025-07-28 07:00:22.972728',	'2025-07-28 07:00:22.972728'),
(14,	'اخلاق حرفه‌ای',	4,	3,	1,	'2025-07-28 07:00:40.024169',	'2025-07-28 07:00:40.024169'),
(15,	'دانش فنی پایه',	5,	3,	1,	'2025-07-28 07:01:13.567842',	'2025-07-28 07:01:13.567842'),
(18,	'کارگاه برق ساختمان',	5,	3,	1,	'2025-07-28 07:02:12.347663',	'2025-07-28 07:02:12.347663'),
(19,	'کارگاه جریان ضعیف',	5,	3,	1,	'2025-07-28 07:02:27.075167',	'2025-07-28 07:02:27.075167'),
(20,	'کارگاه هوشمند',	5,	3,	1,	'2025-07-28 07:02:47.134862',	'2025-07-28 07:02:47.134862'),
(21,	'کارگاه کابل و سیم پیچی',	5,	3,	1,	'2025-07-28 07:03:04.931705',	'2025-07-28 07:03:04.931705'),
(22,	'کارگاه تابلو',	5,	3,	1,	'2025-07-28 07:03:23.071033',	'2025-07-28 07:03:23.071033'),
(23,	'کارگاه رله',	5,	3,	1,	'2025-07-28 07:03:34.813689',	'2025-07-28 07:03:34.813689'),
(17,	'دانش فنی تخصصی',	5,	3,	1,	'2025-07-28 07:01:57.713472',	'2025-07-28 07:33:03.611194'),
(2,	'ادبیات فارسی ۳',	1,	3,	1,	'2025-07-27 03:04:26.194746',	'2025-07-28 06:46:39.549118'),
(24,	'فارسی و نگارش ۳',	1,	3,	1,	'2025-08-03 20:29:29.867106',	'2025-08-03 20:29:29.867106'),
(25,	'کارآفرینی',	4,	3,	1,	'2025-08-03 20:34:05.498798',	'2025-08-03 20:34:05.498798');

DROP TABLE IF EXISTS "exams";
DROP SEQUENCE IF EXISTS exams_id_seq;
CREATE SEQUENCE exams_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."exams" (
    "id" integer DEFAULT nextval('exams_id_seq') NOT NULL,
    "name" character varying NOT NULL,
    "courses" jsonb NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "PK_b43159ee3efa440952794b4f53e" PRIMARY KEY ("id")
)
WITH (oids = false);

INSERT INTO "exams" ("id", "name", "courses", "createdAt", "updatedAt") VALUES
(3,	'آزمون دوره‌ای ۱',	'[{"courseId": 24, "questionCount": 5}, {"courseId": 3, "questionCount": 5}, {"courseId": 4, "questionCount": 5}, {"courseId": 5, "questionCount": 5}, {"courseId": 6, "questionCount": 10}, {"courseId": 7, "questionCount": 10}, {"courseId": 8, "questionCount": 10}, {"courseId": 10, "questionCount": 10}, {"courseId": 11, "questionCount": 8}, {"courseId": 12, "questionCount": 8}, {"courseId": 25, "questionCount": 4}, {"courseId": 14, "questionCount": 8}, {"courseId": 15, "questionCount": 10}, {"courseId": 17, "questionCount": 10}, {"courseId": 18, "questionCount": 10}, {"courseId": 21, "questionCount": 10}]',	'2025-08-03 20:37:08.335704',	'2025-08-03 20:45:40.15134'),
(2,	'آزمون ریاضی',	'[{"courseId": 2, "questionCount": 10}, {"courseId": 1, "questionCount": 10}]',	'2025-07-27 03:04:26.211923',	'2025-07-28 06:13:00.761706'),
(1,	'آزمون فلان شماره ۱ تاریخ فلان',	'[{"courseId": 2, "questionCount": 10}, {"courseId": 3, "questionCount": 10}, {"courseId": 5, "questionCount": 10}, {"courseId": 4, "questionCount": 10}, {"courseId": 6, "questionCount": 5}, {"courseId": 7, "questionCount": 5}, {"courseId": 8, "questionCount": 5}, {"courseId": 9, "questionCount": 10}, {"courseId": 10, "questionCount": 10}, {"courseId": 11, "questionCount": 5}, {"courseId": 12, "questionCount": 5}, {"courseId": 13, "questionCount": 5}, {"courseId": 14, "questionCount": 5}, {"courseId": 15, "questionCount": 9}, {"courseId": 17, "questionCount": 10}, {"courseId": 18, "questionCount": 5}, {"courseId": 19, "questionCount": 8}, {"courseId": 20, "questionCount": 4}, {"courseId": 21, "questionCount": 9}, {"courseId": 22, "questionCount": 7}, {"courseId": 23, "questionCount": 8}]',	'2025-07-27 02:59:03.354458',	'2025-07-28 08:01:32.756666');

DROP TABLE IF EXISTS "questions";
DROP SEQUENCE IF EXISTS questions_id_seq;
CREATE SEQUENCE questions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."questions" (
    "id" integer DEFAULT nextval('questions_id_seq') NOT NULL,
    "exam_id" integer NOT NULL,
    "course_id" integer NOT NULL,
    "question_number" integer NOT NULL,
    "question_text" text NOT NULL,
    "options" jsonb NOT NULL,
    "correct_answer" questions_correct_answer_enum NOT NULL,
    "type" questions_type_enum DEFAULT 'multiple_choice' NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "PK_08a6d4b0f49ff300bf3a0ca60ac" PRIMARY KEY ("id")
)
WITH (oids = false);

INSERT INTO "questions" ("id", "exam_id", "course_id", "question_number", "question_text", "options", "correct_answer", "type", "createdAt", "updatedAt") VALUES
(1,	3,	24,	1,	'1',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:02:10.991859',	'2025-08-03 23:02:10.991859'),
(2,	3,	24,	2,	'2',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:02:30.59009',	'2025-08-03 23:02:30.59009'),
(3,	3,	24,	3,	'3',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:04:12.776453',	'2025-08-03 23:04:12.776453'),
(4,	3,	24,	4,	'4',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:04:42.964031',	'2025-08-03 23:04:42.964031'),
(5,	3,	24,	5,	'5',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:04:58.224044',	'2025-08-03 23:04:58.224044'),
(6,	3,	3,	6,	'6',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:05:15.427743',	'2025-08-03 23:05:15.427743'),
(7,	3,	3,	7,	'7',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:05:34.144609',	'2025-08-03 23:05:34.144609'),
(8,	3,	3,	8,	'8',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:05:48.863846',	'2025-08-03 23:05:48.863846'),
(9,	3,	3,	9,	'9',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:06:38.924396',	'2025-08-03 23:06:38.924396'),
(10,	3,	3,	10,	'10',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:06:55.271337',	'2025-08-03 23:06:55.271337'),
(11,	3,	4,	11,	'11',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:07:28.395273',	'2025-08-03 23:07:28.395273'),
(12,	3,	4,	12,	'12',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:07:38.711519',	'2025-08-03 23:07:38.711519'),
(13,	3,	4,	13,	'13',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:07:49.780231',	'2025-08-03 23:07:49.780231'),
(14,	3,	4,	14,	'14',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:08:12.277243',	'2025-08-03 23:08:12.277243'),
(15,	3,	4,	15,	'15',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:08:59.985089',	'2025-08-03 23:08:59.985089'),
(16,	3,	5,	16,	'16',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:09:13.723393',	'2025-08-03 23:09:13.723393'),
(17,	3,	5,	17,	'17',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:09:26.965663',	'2025-08-03 23:09:26.965663'),
(18,	3,	5,	18,	'18',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:09:36.912531',	'2025-08-03 23:09:36.912531'),
(19,	3,	5,	19,	'19',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:09:50.207316',	'2025-08-03 23:09:50.207316'),
(20,	3,	5,	20,	'20',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:09:59.145699',	'2025-08-03 23:09:59.145699'),
(21,	3,	6,	21,	'21',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:10:12.443864',	'2025-08-03 23:10:12.443864'),
(22,	3,	6,	22,	'22',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:10:34.731278',	'2025-08-03 23:10:34.731278'),
(23,	3,	6,	23,	'23',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:11:20.817579',	'2025-08-03 23:11:20.817579'),
(24,	3,	6,	24,	'24',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:11:59.797111',	'2025-08-03 23:11:59.797111'),
(25,	3,	6,	25,	'25',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:12:11.262204',	'2025-08-03 23:12:11.262204'),
(26,	3,	6,	26,	'26',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:12:19.411087',	'2025-08-03 23:12:19.411087'),
(27,	3,	6,	27,	'27',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:12:28.823489',	'2025-08-03 23:12:28.823489'),
(28,	3,	6,	28,	'28',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:12:37.199785',	'2025-08-03 23:12:37.199785'),
(29,	3,	6,	29,	'29',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:12:49.437057',	'2025-08-03 23:12:49.437057'),
(30,	3,	6,	30,	'30',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:15:00.111534',	'2025-08-03 23:15:00.111534'),
(31,	3,	7,	31,	'31',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:15:13.30333',	'2025-08-03 23:15:13.30333'),
(32,	3,	7,	32,	'32',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:15:22.040099',	'2025-08-03 23:15:22.040099'),
(33,	3,	7,	33,	'33',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:15:33.899575',	'2025-08-03 23:15:33.899575'),
(34,	3,	7,	34,	'34',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:15:46.956005',	'2025-08-03 23:15:46.956005'),
(35,	3,	7,	35,	'35',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:15:56.403236',	'2025-08-03 23:15:56.403236'),
(36,	3,	7,	36,	'36',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:16:16.21123',	'2025-08-03 23:16:16.21123'),
(37,	3,	7,	37,	'37',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:16:28.809679',	'2025-08-03 23:16:28.809679'),
(38,	3,	7,	38,	'38',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:16:40.468811',	'2025-08-03 23:16:40.468811'),
(39,	3,	7,	39,	'39',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:17:02.039876',	'2025-08-03 23:17:02.039876'),
(40,	3,	7,	40,	'40',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:17:16.629319',	'2025-08-03 23:17:16.629319'),
(41,	3,	8,	41,	'41',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:17:25.334405',	'2025-08-03 23:17:25.334405'),
(42,	3,	8,	42,	'42',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:19:18.405436',	'2025-08-03 23:19:18.405436'),
(43,	3,	8,	43,	'43',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:19:31.446554',	'2025-08-03 23:19:31.446554'),
(44,	3,	8,	44,	'44',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:19:40.024634',	'2025-08-03 23:19:40.024634'),
(45,	3,	8,	45,	'45',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:19:53.31881',	'2025-08-03 23:19:53.31881'),
(46,	3,	8,	46,	'46',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:20:07.236415',	'2025-08-03 23:20:07.236415'),
(47,	3,	8,	47,	'47',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:20:32.955072',	'2025-08-03 23:20:32.955072'),
(48,	3,	8,	48,	'48',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:20:46.981711',	'2025-08-03 23:20:46.981711'),
(49,	3,	8,	49,	'49',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:20:58.277524',	'2025-08-03 23:20:58.277524'),
(50,	3,	8,	50,	'50',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:21:08.729639',	'2025-08-03 23:21:08.729639'),
(51,	3,	10,	51,	'51',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:21:25.847022',	'2025-08-03 23:21:25.847022'),
(52,	3,	10,	52,	'52',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:21:51.307235',	'2025-08-03 23:21:51.307235'),
(53,	3,	10,	53,	'53',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:22:06.252705',	'2025-08-03 23:22:06.252705'),
(54,	3,	10,	54,	'54',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:23:49.914463',	'2025-08-03 23:23:49.914463'),
(55,	3,	10,	55,	'55',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:24:02.382654',	'2025-08-03 23:24:02.382654'),
(56,	3,	10,	56,	'56',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:24:15.120868',	'2025-08-03 23:24:15.120868'),
(57,	3,	10,	57,	'57',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:24:25.052897',	'2025-08-03 23:24:25.052897'),
(58,	3,	10,	58,	'58',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:24:39.951089',	'2025-08-03 23:24:39.951089'),
(59,	3,	10,	59,	'59',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:24:52.664222',	'2025-08-03 23:24:52.664222'),
(60,	3,	10,	60,	'60',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:25:06.414955',	'2025-08-03 23:25:06.414955'),
(61,	3,	11,	61,	'61',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:25:19.009224',	'2025-08-03 23:25:19.009224'),
(62,	3,	11,	62,	'62',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:25:47.464179',	'2025-08-03 23:25:47.464179'),
(63,	3,	11,	63,	'63',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:26:06.517851',	'2025-08-03 23:26:06.517851'),
(64,	3,	11,	64,	'64',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:26:35.062494',	'2025-08-03 23:26:35.062494'),
(65,	3,	11,	65,	'65',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:26:57.227476',	'2025-08-03 23:26:57.227476'),
(66,	3,	11,	66,	'66',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:27:09.272777',	'2025-08-03 23:27:09.272777'),
(67,	3,	11,	67,	'67',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:27:32.09357',	'2025-08-03 23:27:32.09357'),
(68,	3,	11,	68,	'68',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:27:51.546451',	'2025-08-03 23:27:51.546451'),
(69,	3,	12,	69,	'69',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:28:05.330046',	'2025-08-03 23:28:05.330046'),
(70,	3,	12,	70,	'70',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:28:20.171171',	'2025-08-03 23:28:20.171171'),
(71,	3,	12,	71,	'71',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:28:30.852983',	'2025-08-03 23:28:30.852983'),
(72,	3,	12,	72,	'72',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:28:57.272609',	'2025-08-03 23:28:57.272609'),
(73,	3,	12,	73,	'73',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:29:10.164659',	'2025-08-03 23:29:10.164659'),
(74,	3,	12,	74,	'74',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:29:25.656426',	'2025-08-03 23:29:25.656426'),
(75,	3,	12,	75,	'75',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:30:15.086364',	'2025-08-03 23:30:15.086364'),
(76,	3,	12,	76,	'76',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:30:25.699441',	'2025-08-03 23:30:25.699441'),
(77,	3,	25,	77,	'77',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:30:55.888241',	'2025-08-03 23:30:55.888241'),
(78,	3,	25,	78,	'78',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:31:10.421462',	'2025-08-03 23:31:10.421462'),
(79,	3,	25,	79,	'79',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:31:32.642201',	'2025-08-03 23:31:32.642201'),
(80,	3,	25,	80,	'80',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:31:50.069796',	'2025-08-03 23:31:50.069796'),
(81,	3,	14,	81,	'81',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:32:10.319956',	'2025-08-03 23:32:10.319956'),
(82,	3,	14,	82,	'82',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:32:38.487844',	'2025-08-03 23:32:38.487844'),
(83,	3,	14,	83,	'83',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:33:10.834131',	'2025-08-03 23:33:10.834131'),
(84,	3,	14,	84,	'84',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:33:40.54225',	'2025-08-03 23:33:40.54225'),
(85,	3,	14,	85,	'88',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:34:05.360772',	'2025-08-03 23:34:05.360772'),
(86,	3,	14,	86,	'86',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:34:26.14727',	'2025-08-03 23:34:26.14727'),
(87,	3,	14,	87,	'87',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:34:48.829485',	'2025-08-03 23:34:48.829485'),
(88,	3,	14,	88,	'88',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:35:36.428175',	'2025-08-03 23:35:36.428175'),
(89,	3,	15,	89,	'89',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:35:52.122347',	'2025-08-03 23:35:52.122347'),
(90,	3,	15,	90,	'90',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:36:05.108025',	'2025-08-03 23:36:05.108025'),
(91,	3,	15,	91,	'91',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:36:28.384207',	'2025-08-03 23:36:28.384207'),
(92,	3,	15,	92,	'92',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:36:40.111337',	'2025-08-03 23:36:40.111337'),
(93,	3,	15,	93,	'93',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:36:53.2669',	'2025-08-03 23:36:53.2669'),
(94,	3,	15,	94,	'94',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:37:04.457504',	'2025-08-03 23:37:04.457504'),
(95,	3,	15,	95,	'95',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:37:15.983786',	'2025-08-03 23:37:15.983786'),
(96,	3,	15,	96,	'96',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:37:27.367444',	'2025-08-03 23:37:27.367444'),
(97,	3,	15,	97,	'97',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:37:41.663701',	'2025-08-03 23:37:41.663701'),
(98,	3,	15,	98,	'98',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:37:55.020983',	'2025-08-03 23:37:55.020983'),
(99,	3,	17,	99,	'99',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:38:19.366924',	'2025-08-03 23:38:19.366924'),
(100,	3,	17,	100,	'100',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:38:32.040412',	'2025-08-03 23:38:32.040412'),
(101,	3,	17,	101,	'101',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:38:46.916962',	'2025-08-03 23:38:46.916962'),
(102,	3,	17,	102,	'102',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:39:03.737643',	'2025-08-03 23:39:03.737643'),
(103,	3,	17,	103,	'103',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:39:37.61626',	'2025-08-03 23:39:37.61626'),
(104,	3,	17,	104,	'104',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:39:53.511908',	'2025-08-03 23:39:53.511908'),
(105,	3,	17,	105,	'105',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:40:03.646641',	'2025-08-03 23:40:03.646641'),
(106,	3,	17,	106,	'106',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:40:14.574113',	'2025-08-03 23:40:14.574113'),
(107,	3,	17,	107,	'107',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:40:25.938109',	'2025-08-03 23:40:25.938109'),
(108,	3,	17,	108,	'108',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:40:44.840791',	'2025-08-03 23:40:44.840791'),
(109,	3,	18,	109,	'109',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:41:02.355624',	'2025-08-03 23:41:02.355624'),
(110,	3,	18,	110,	'110',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:41:19.094975',	'2025-08-03 23:41:19.094975'),
(111,	3,	18,	111,	'111',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:41:29.887342',	'2025-08-03 23:41:29.887342'),
(112,	3,	18,	112,	'112',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:41:39.395336',	'2025-08-03 23:41:39.395336'),
(113,	3,	18,	113,	'113',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:41:54.416835',	'2025-08-03 23:41:54.416835'),
(114,	3,	18,	114,	'114',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:42:03.525309',	'2025-08-03 23:42:03.525309'),
(115,	3,	18,	115,	'115',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:46:58.710697',	'2025-08-03 23:46:58.710697'),
(116,	3,	18,	116,	'116',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:47:09.014394',	'2025-08-03 23:47:09.014394'),
(118,	3,	21,	118,	'118',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:47:38.639549',	'2025-08-03 23:47:38.639549'),
(119,	3,	21,	119,	'119',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:47:57.864582',	'2025-08-03 23:47:57.864582'),
(120,	3,	21,	120,	'120',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:48:11.400992',	'2025-08-03 23:48:11.400992'),
(121,	3,	21,	121,	'121',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:48:52.479864',	'2025-08-03 23:48:52.479864'),
(122,	3,	21,	122,	'122',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:49:03.574803',	'2025-08-03 23:49:03.574803'),
(123,	3,	21,	123,	'123',	'["1", "2", "3", "4"]',	'A',	'multiple_choice',	'2025-08-03 23:49:23.345252',	'2025-08-03 23:49:23.345252'),
(124,	3,	21,	124,	'124',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:49:34.387669',	'2025-08-03 23:49:34.387669'),
(125,	3,	21,	125,	'125',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:49:45.03127',	'2025-08-03 23:49:45.03127'),
(126,	3,	21,	126,	'126',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:49:56.671511',	'2025-08-03 23:49:56.671511'),
(127,	3,	21,	127,	'127',	'["1", "2", "3", "4"]',	'B',	'multiple_choice',	'2025-08-03 23:50:12.911453',	'2025-08-03 23:50:12.911453'),
(128,	3,	21,	128,	'128',	'["1", "2", "3", "4"]',	'C',	'multiple_choice',	'2025-08-03 23:50:26.798247',	'2025-08-03 23:50:26.798247'),
(117,	3,	18,	117,	'117',	'["1", "2", "3", "4"]',	'D',	'multiple_choice',	'2025-08-03 23:47:20.143171',	'2025-08-08 16:52:47.077652');

DROP TABLE IF EXISTS "sessions";
DROP SEQUENCE IF EXISTS sessions_id_seq;
CREATE SEQUENCE sessions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."sessions" (
    "id" integer DEFAULT nextval('sessions_id_seq') NOT NULL,
    "exam_id" integer NOT NULL,
    "user_id" integer NOT NULL,
    "total_score" numeric(5,2) NOT NULL,
    "score_details" jsonb NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    "rank" integer,
    CONSTRAINT "PK_3238ef96f18b355b671619111bc" PRIMARY KEY ("id")
)
WITH (oids = false);

INSERT INTO "sessions" ("id", "exam_id", "user_id", "total_score", "score_details", "createdAt", "updatedAt", "rank") VALUES
(5,	2,	6,	-30.00,	'{"answers": [{"courseId": 2, "unanswered": 3, "wrongAnswers": 6, "correctAnswers": 1}, {"courseId": 1, "unanswered": 0, "wrongAnswers": 8, "correctAnswers": 2}], "courseScores": [{"courseId": 2, "courseName": "ادبیات فارسی ۳", "percentage": -10}], "courseGroupScores": [{"averageScore": -10, "courseGroupId": 1, "weightedScore": -30, "courseGroupName": "دروس عمومی"}]}',	'2025-07-28 06:15:36.780438',	'2025-07-31 21:15:02.906127',	NULL),
(4,	1,	3,	0.00,	'{"answers": [{"courseId": 1, "unanswered": 3, "wrongAnswers": 6, "correctAnswers": 1}], "courseScores": [], "courseGroupScores": [{"averageScore": 0, "courseGroupId": 4, "weightedScore": 0, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 0, "courseGroupId": 3, "weightedScore": 0, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 0, "courseGroupId": 1, "weightedScore": 0, "courseGroupName": "دروس عمومی"}, {"averageScore": 0, "courseGroupId": 5, "weightedScore": 0, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-07-27 03:05:26.240178',	'2025-07-31 19:06:54.546304',	NULL),
(1,	1,	3,	0.00,	'{"answers": [{"courseId": 1, "unanswered": 1, "wrongAnswers": 6, "correctAnswers": 3}], "courseScores": [], "courseGroupScores": [{"averageScore": 0, "courseGroupId": 4, "weightedScore": 0, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 0, "courseGroupId": 3, "weightedScore": 0, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 0, "courseGroupId": 1, "weightedScore": 0, "courseGroupName": "دروس عمومی"}, {"averageScore": 0, "courseGroupId": 5, "weightedScore": 0, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-07-27 03:00:27.114069',	'2025-07-31 19:06:56.209451',	NULL),
(2,	1,	3,	0.00,	'{"answers": [{"courseId": 1, "unanswered": 3, "wrongAnswers": 6, "correctAnswers": 1}], "courseScores": [], "courseGroupScores": [{"averageScore": 0, "courseGroupId": 4, "weightedScore": 0, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 0, "courseGroupId": 3, "weightedScore": 0, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 0, "courseGroupId": 1, "weightedScore": 0, "courseGroupName": "دروس عمومی"}, {"averageScore": 0, "courseGroupId": 5, "weightedScore": 0, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-07-27 03:01:10.840284',	'2025-07-31 19:06:58.141963',	NULL),
(3,	2,	4,	-6.67,	'{"answers": [{"courseId": 2, "unanswered": 0, "wrongAnswers": 8, "correctAnswers": 2}], "courseScores": [{"courseId": 2, "courseName": "ادبیات فارسی ۳", "percentage": -6.67}], "courseGroupScores": [{"averageScore": -6.67, "courseGroupId": 1, "weightedScore": -6.67, "courseGroupName": "دروس عمومی"}]}',	'2025-07-27 03:04:26.230724',	'2025-07-31 19:06:59.766396',	NULL),
(7,	3,	8,	145.63,	'{"answers": [{"courseId": 24, "unanswered": 3, "wrongAnswers": 1, "correctAnswers": 1}, {"courseId": 3, "unanswered": 4, "wrongAnswers": 1, "correctAnswers": 0}, {"courseId": 4, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 0}, {"courseId": 5, "unanswered": 0, "wrongAnswers": 1, "correctAnswers": 4}, {"courseId": 6, "unanswered": 5, "wrongAnswers": 0, "correctAnswers": 5}, {"courseId": 7, "unanswered": 5, "wrongAnswers": 3, "correctAnswers": 2}, {"courseId": 8, "unanswered": 6, "wrongAnswers": 2, "correctAnswers": 2}, {"courseId": 10, "unanswered": 2, "wrongAnswers": 4, "correctAnswers": 4}, {"courseId": 11, "unanswered": 1, "wrongAnswers": 3, "correctAnswers": 4}, {"courseId": 12, "unanswered": 2, "wrongAnswers": 5, "correctAnswers": 1}, {"courseId": 25, "unanswered": 1, "wrongAnswers": 2, "correctAnswers": 1}, {"courseId": 14, "unanswered": 3, "wrongAnswers": 3, "correctAnswers": 2}, {"courseId": 15, "unanswered": 3, "wrongAnswers": 1, "correctAnswers": 6}, {"courseId": 17, "unanswered": 4, "wrongAnswers": 2, "correctAnswers": 4}, {"courseId": 18, "unanswered": 7, "wrongAnswers": 1, "correctAnswers": 1}, {"courseId": 21, "unanswered": 5, "wrongAnswers": 3, "correctAnswers": 3}], "courseScores": [{"courseId": 24, "courseName": "فارسی و نگارش ۳", "percentage": 13.33}, {"courseId": 3, "courseName": "عربی ۳", "percentage": -6.67}, {"courseId": 4, "courseName": "دین و زندگی ۳", "percentage": -20}, {"courseId": 5, "courseName": "زبان انگلیسی", "percentage": 73.33}, {"courseId": 6, "courseName": "ریاضی ۱", "percentage": 50}, {"courseId": 7, "courseName": "ریاضی ۲", "percentage": 10}, {"courseId": 8, "courseName": "ریاضی ۳", "percentage": 13.33}, {"courseId": 10, "courseName": "فیزیک", "percentage": 26.67}, {"courseId": 11, "courseName": "الزامات محیط کار", "percentage": 37.5}, {"courseId": 12, "courseName": "فناوری نوین", "percentage": -8.33}, {"courseId": 25, "courseName": "کارآفرینی", "percentage": 8.33}, {"courseId": 14, "courseName": "اخلاق حرفه‌ای", "percentage": 12.5}, {"courseId": 15, "courseName": "دانش فنی پایه", "percentage": 56.67}, {"courseId": 17, "courseName": "دانش فنی تخصصی", "percentage": 33.33}, {"courseId": 18, "courseName": "کارگاه برق ساختمان", "percentage": 6.67}, {"courseId": 21, "courseName": "کارگاه کابل و سیم پیچی", "percentage": 20}], "courseGroupScores": [{"averageScore": 14.997499999999999, "courseGroupId": 1, "weightedScore": 44.99, "courseGroupName": "دروس عمومی"}, {"averageScore": 12.5, "courseGroupId": 4, "weightedScore": 37.5, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 25, "courseGroupId": 3, "weightedScore": 150, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 29.1675, "courseGroupId": 5, "weightedScore": 350.01, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-08-03 23:57:36.64654',	'2025-08-08 19:08:37.117366',	NULL),
(8,	3,	9,	172.35,	'{"answers": [{"courseId": 24, "unanswered": 3, "wrongAnswers": 1, "correctAnswers": 1}, {"courseId": 3, "unanswered": 5, "wrongAnswers": 0, "correctAnswers": 0}, {"courseId": 4, "unanswered": 5, "wrongAnswers": 0, "correctAnswers": 0}, {"courseId": 5, "unanswered": 0, "wrongAnswers": 1, "correctAnswers": 4}, {"courseId": 6, "unanswered": 7, "wrongAnswers": 0, "correctAnswers": 3}, {"courseId": 7, "unanswered": 9, "wrongAnswers": 0, "correctAnswers": 1}, {"courseId": 8, "unanswered": 10, "wrongAnswers": 0, "correctAnswers": 0}, {"courseId": 10, "unanswered": 7, "wrongAnswers": 2, "correctAnswers": 1}, {"courseId": 11, "unanswered": 1, "wrongAnswers": 3, "correctAnswers": 4}, {"courseId": 12, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 3}, {"courseId": 25, "unanswered": 0, "wrongAnswers": 1, "correctAnswers": 3}, {"courseId": 14, "unanswered": 4, "wrongAnswers": 2, "correctAnswers": 2}, {"courseId": 15, "unanswered": 8, "wrongAnswers": 1, "correctAnswers": 1}, {"courseId": 17, "unanswered": 3, "wrongAnswers": 2, "correctAnswers": 5}, {"courseId": 18, "unanswered": 2, "wrongAnswers": 2, "correctAnswers": 5}, {"courseId": 21, "unanswered": 4, "wrongAnswers": 1, "correctAnswers": 6}], "courseScores": [{"courseId": 24, "courseName": "فارسی و نگارش ۳", "percentage": 13.33}, {"courseId": 3, "courseName": "عربی ۳", "percentage": 0}, {"courseId": 4, "courseName": "دین و زندگی ۳", "percentage": 0}, {"courseId": 5, "courseName": "زبان انگلیسی", "percentage": 73.33}, {"courseId": 6, "courseName": "ریاضی ۱", "percentage": 30}, {"courseId": 7, "courseName": "ریاضی ۲", "percentage": 10}, {"courseId": 8, "courseName": "ریاضی ۳", "percentage": 0}, {"courseId": 10, "courseName": "فیزیک", "percentage": 3.33}, {"courseId": 11, "courseName": "الزامات محیط کار", "percentage": 37.5}, {"courseId": 12, "courseName": "فناوری نوین", "percentage": 25}, {"courseId": 25, "courseName": "کارآفرینی", "percentage": 66.67}, {"courseId": 14, "courseName": "اخلاق حرفه‌ای", "percentage": 16.67}, {"courseId": 15, "courseName": "دانش فنی پایه", "percentage": 6.67}, {"courseId": 17, "courseName": "دانش فنی تخصصی", "percentage": 43.33}, {"courseId": 18, "courseName": "کارگاه برق ساختمان", "percentage": 43.33}, {"courseId": 21, "courseName": "کارگاه کابل و سیم پیچی", "percentage": 56.67}], "courseGroupScores": [{"averageScore": 21.665, "courseGroupId": 1, "weightedScore": 65, "courseGroupName": "دروس عمومی"}, {"averageScore": 36.46, "courseGroupId": 4, "weightedScore": 109.38, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 10.8325, "courseGroupId": 3, "weightedScore": 65, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 37.49999999999999, "courseGroupId": 5, "weightedScore": 450, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-08-04 23:06:07.559984',	'2025-08-08 19:08:39.599002',	NULL),
(6,	1,	6,	42.07,	'{"answers": [{"courseId": 2, "unanswered": 3, "wrongAnswers": 6, "correctAnswers": 1}, {"courseId": 3, "unanswered": 0, "wrongAnswers": 8, "correctAnswers": 2}, {"courseId": 5, "unanswered": 4, "wrongAnswers": 4, "correctAnswers": 2}, {"courseId": 4, "unanswered": 2, "wrongAnswers": 6, "correctAnswers": 2}, {"courseId": 6, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 0}, {"courseId": 7, "unanswered": 2, "wrongAnswers": 1, "correctAnswers": 2}, {"courseId": 8, "unanswered": 2, "wrongAnswers": 2, "correctAnswers": 1}, {"courseId": 9, "unanswered": 2, "wrongAnswers": 4, "correctAnswers": 4}, {"courseId": 10, "unanswered": 5, "wrongAnswers": 2, "correctAnswers": 3}, {"courseId": 11, "unanswered": 2, "wrongAnswers": 2, "correctAnswers": 1}, {"courseId": 12, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 0}, {"courseId": 13, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 0}, {"courseId": 14, "unanswered": 2, "wrongAnswers": 0, "correctAnswers": 3}, {"courseId": 15, "unanswered": 5, "wrongAnswers": 4, "correctAnswers": 0}, {"courseId": 17, "unanswered": 4, "wrongAnswers": 4, "correctAnswers": 2}, {"courseId": 18, "unanswered": 2, "wrongAnswers": 3, "correctAnswers": 0}, {"courseId": 19, "unanswered": 4, "wrongAnswers": 4, "correctAnswers": 0}, {"courseId": 20, "unanswered": 1, "wrongAnswers": 1, "correctAnswers": 2}, {"courseId": 21, "unanswered": 4, "wrongAnswers": 3, "correctAnswers": 2}, {"courseId": 22, "unanswered": 3, "wrongAnswers": 2, "correctAnswers": 2}, {"courseId": 23, "unanswered": 1, "wrongAnswers": 4, "correctAnswers": 3}], "courseScores": [{"courseId": 2, "courseName": "ادبیات فارسی ۳", "percentage": -10}, {"courseId": 3, "courseName": "عربی ۳", "percentage": -6.67}, {"courseId": 5, "courseName": "زبان انگلیسی", "percentage": 6.67}, {"courseId": 4, "courseName": "دین و زندگی ۳", "percentage": 0}, {"courseId": 6, "courseName": "ریاضی ۱", "percentage": -20}, {"courseId": 7, "courseName": "ریاضی ۲", "percentage": 33.33}, {"courseId": 8, "courseName": "ریاضی ۳", "percentage": 6.67}, {"courseId": 9, "courseName": "شیمی", "percentage": 26.67}, {"courseId": 10, "courseName": "فیزیک", "percentage": 23.33}, {"courseId": 11, "courseName": "الزامات محیط کار", "percentage": 6.67}, {"courseId": 12, "courseName": "فناوری نوین", "percentage": -20}, {"courseId": 13, "courseName": "کارگاه نوآوری", "percentage": -20}, {"courseId": 14, "courseName": "اخلاق حرفه‌ای", "percentage": 60}, {"courseId": 15, "courseName": "دانش فنی پایه", "percentage": -14.81}, {"courseId": 17, "courseName": "دانش فنی تخصصی", "percentage": 6.67}, {"courseId": 18, "courseName": "کارگاه برق ساختمان", "percentage": -20}, {"courseId": 19, "courseName": "کارگاه جریان ضعیف", "percentage": -16.67}, {"courseId": 20, "courseName": "کارگاه هوشمند", "percentage": 41.67}, {"courseId": 21, "courseName": "کارگاه کابل و سیم پیچی", "percentage": 11.11}, {"courseId": 22, "courseName": "کارگاه تابلو", "percentage": 19.05}, {"courseId": 23, "courseName": "کارگاه رله", "percentage": 20.83}], "courseGroupScores": [{"averageScore": -2.5, "courseGroupId": 1, "weightedScore": -7.5, "courseGroupName": "دروس عمومی"}, {"averageScore": 6.6675, "courseGroupId": 4, "weightedScore": 20, "courseGroupName": "دروس شایستگی غیر فنی"}, {"averageScore": 14, "courseGroupId": 3, "weightedScore": 84, "courseGroupName": "دروس شایستگی پایه"}, {"averageScore": 5.981249999999999, "courseGroupId": 5, "weightedScore": 71.77, "courseGroupName": "دروس شایستگی فنی"}]}',	'2025-07-28 08:04:28.641322',	'2025-08-08 19:08:43.099622',	NULL);

DROP TABLE IF EXISTS "users";
DROP SEQUENCE IF EXISTS users_id_seq;
CREATE SEQUENCE users_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."users" (
    "id" integer DEFAULT nextval('users_id_seq') NOT NULL,
    "username" character varying NOT NULL,
    "password" character varying NOT NULL,
    "role" users_role_enum DEFAULT 'user' NOT NULL,
    "createdAt" timestamp DEFAULT now() NOT NULL,
    "updatedAt" timestamp DEFAULT now() NOT NULL,
    "name" character varying,
    "lastName" character varying,
    "profileImage" character varying,
    CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
)
WITH (oids = false);

CREATE UNIQUE INDEX "UQ_fe0bb3f6520ee0469504521e710" ON public.users USING btree (username);

INSERT INTO "users" ("id", "username", "password", "role", "createdAt", "updatedAt", "name", "lastName", "profileImage") VALUES
(1,	'admin',	'$2b$10$ULA9x8L6KbYhwTIni56bq.sbdCStCNg.ohWpJiB5wCkSOt6nQV6S6',	'admin',	'2025-07-27 02:46:28.493488',	'2025-07-27 02:46:28.493488',	NULL,	NULL,	NULL),
(2,	'testuser',	'$2b$10$qy6ECEfuLBXNw2a.pHzsR.3xkJ4FRvMv7HpiCz6PEcswRkE/Ns5VO',	'user',	'2025-07-27 02:47:27.34992',	'2025-07-27 02:47:27.34992',	NULL,	NULL,	NULL),
(3,	'Ramin',	'$2b$10$PvoIYe25IbmtVBcvItXYze43o1okY4ORxgaW2ooOy2nW4dbSkscnq',	'user',	'2025-07-27 02:54:31.820989',	'2025-07-29 02:07:48.018451',	'رامین',	'فیروز',	NULL),
(6,	'student',	'$2b$10$XOHWWF5fi2fDzMb6eGHN0O1Ai5QCiCD2HgGaIfHO9bOlfG5yqt3WK',	'user',	'2025-07-28 06:11:58.688845',	'2025-07-29 02:08:14.195262',	'مسعود',	'فردمنش',	NULL),
(5,	'admin1',	'$2b$10$ZKlewNYdnYOpJ3XPSdmOYOB3CQn.qsl1Tz43NELB0KUms4shbljS6',	'admin',	'2025-07-28 06:07:47.3331',	'2025-07-31 17:26:52.46218',	NULL,	NULL,	NULL),
(4,	'student1',	'$2b$10$lE7RY8bH6eENGHJvPr7yiO1VVm21siJsRs/nu40/25Uz54kbtTFJ6',	'user',	'2025-07-27 03:04:26.158155',	'2025-07-31 17:27:45.694591',	NULL,	NULL,	NULL),
(7,	'vhid_pakdl',	'$2b$10$Sl.4rKkAIIp0mF9RcBQA8.3X0yg4ZVeZkguG7Y8URFZL3PnMqNxAe',	'user',	'2025-08-03 21:16:22.799459',	'2025-08-03 21:16:22.799459',	'وحید',	'پاکدل',	NULL),
(9,	'mslm_bhrami',	'$2b$10$J0lnsdIQioPmNkUP1sMSR.he0csEPjRYaLttsvGBMozzZw95aIjGW',	'user',	'2025-08-04 22:54:45.473161',	'2025-08-08 18:01:26.104273',	'مسلم',	'بهرامی',	'user_9_1754663486076.jpg'),
(8,	'mhdi_bkhshi',	'$2b$10$9kLYwOmBxajBekXCCrsAoudB6dOjwYHiAXObJ90VrM3QF6qHnwrpi',	'user',	'2025-08-03 21:21:37.57074',	'2025-08-08 19:00:15.66513',	'مهدی',	'بخشی',	'user_8_1754667015657.jpg');

ALTER TABLE ONLY "public"."courses" ADD CONSTRAINT "FK_c3d68388dfd2fd62cde6249f713" FOREIGN KEY (course_group_id) REFERENCES course_groups(id) NOT DEFERRABLE;

ALTER TABLE ONLY "public"."questions" ADD CONSTRAINT "FK_921085ed8cad9bd8299dc603ef4" FOREIGN KEY (course_id) REFERENCES courses(id) NOT DEFERRABLE;
ALTER TABLE ONLY "public"."questions" ADD CONSTRAINT "FK_f912d2c24bc84f66e0a40b1c169" FOREIGN KEY (exam_id) REFERENCES exams(id) NOT DEFERRABLE;

ALTER TABLE ONLY "public"."sessions" ADD CONSTRAINT "FK_085d540d9f418cfbdc7bd55bb19" FOREIGN KEY (user_id) REFERENCES users(id) NOT DEFERRABLE;
ALTER TABLE ONLY "public"."sessions" ADD CONSTRAINT "FK_50e00fd88ad06308386daf7cdd5" FOREIGN KEY (exam_id) REFERENCES exams(id) NOT DEFERRABLE;

-- 2025-08-08 16:31:48 UTC
