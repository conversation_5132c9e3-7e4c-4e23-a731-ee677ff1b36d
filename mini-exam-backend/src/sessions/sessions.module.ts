import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SessionsService } from './sessions.service';
import { SessionsController } from './sessions.controller';
import { Session } from '../entities/session.entity';
import { SessionAnswer } from '../entities/session-answer.entity';
import { Exam } from '../entities/exam.entity';
import { User } from '../entities/user.entity';
import { Question } from '../entities/question.entity';
import { ScoringModule } from '../scoring/scoring.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Session, SessionAnswer, Exam, User, Question]),
    ScoringModule,
  ],
  controllers: [SessionsController],
  providers: [SessionsService],
  exports: [SessionsService],
})
export class SessionsModule {}
