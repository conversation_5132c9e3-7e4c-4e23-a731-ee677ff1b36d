import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Session } from '../entities/session.entity';
import { Exam } from '../entities/exam.entity';
import { User } from '../entities/user.entity';
import { Question } from '../entities/question.entity';
import { ScoringService } from '../scoring/scoring.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { CreateSessionByQuestionsDto } from './dto/create-session-by-questions.dto';

@Injectable()
export class SessionsService {
  constructor(
    @InjectRepository(Session)
    private sessionRepository: Repository<Session>,
    @InjectRepository(Exam)
    private examRepository: Repository<Exam>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    private scoringService: ScoringService,
  ) {}

  async create(createSessionDto: CreateSessionDto, userId: number): Promise<Session> {
    // Verify exam exists
    const exam = await this.examRepository.findOne({
      where: { id: createSessionDto.examId },
    });

    if (!exam) {
      throw new NotFoundException(`Exam with ID ${createSessionDto.examId} not found`);
    }

    // Verify user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Validate answers match exam courses
    const examCourseIds = exam.courses.map(c => c.courseId);
    const answerCourseIds = createSessionDto.answers.map(a => a.courseId);
    
    const missingCourses = examCourseIds.filter(id => !answerCourseIds.includes(id));
    const extraCourses = answerCourseIds.filter(id => !examCourseIds.includes(id));
    
    if (missingCourses.length > 0) {
      throw new BadRequestException(`Missing answers for courses: ${missingCourses.join(', ')}`);
    }
    
    if (extraCourses.length > 0) {
      throw new BadRequestException(`Extra answers for courses not in exam: ${extraCourses.join(', ')}`);
    }

    // Validate answer counts match question counts
    for (const answer of createSessionDto.answers) {
      const examCourse = exam.courses.find(c => c.courseId === answer.courseId);
      if (!examCourse) continue;
      
      const totalAnswered = answer.correctAnswers + answer.wrongAnswers + answer.unanswered;
      if (totalAnswered !== examCourse.questionCount) {
        throw new BadRequestException(
          `Total answers (${totalAnswered}) for course ${answer.courseId} doesn't match question count (${examCourse.questionCount})`
        );
      }
    }

    // Calculate scores
    const { scoreDetails, totalScore } = await this.scoringService.calculateScores(
      exam,
      createSessionDto.answers,
    );

    // Create and save session
    const session = this.sessionRepository.create({
      examId: createSessionDto.examId,
      userId,
      totalScore,
      scoreDetails,
    });

    return this.sessionRepository.save(session);
  }

  async createByQuestions(createSessionDto: CreateSessionByQuestionsDto, userId: number): Promise<Session> {
    // Verify exam exists
    const exam = await this.examRepository.findOne({
      where: { id: createSessionDto.examId },
      relations: ['questions'],
    });

    if (!exam) {
      throw new NotFoundException(`Exam with ID ${createSessionDto.examId} not found`);
    }

    // Verify user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get all questions for this exam
    const questions = await this.questionRepository.find({
      where: { examId: createSessionDto.examId },
      relations: ['course'],
      order: { questionNumber: 'ASC' },
    });

    if (questions.length === 0) {
      throw new BadRequestException('No questions found for this exam');
    }

    // Validate question answers
    const questionIds = questions.map(q => q.id);
    const answerQuestionIds = createSessionDto.questionAnswers.map(qa => qa.questionId);

    const missingQuestions = questionIds.filter(id => !answerQuestionIds.includes(id));
    const extraQuestions = answerQuestionIds.filter(id => !questionIds.includes(id));

    if (missingQuestions.length > 0) {
      throw new BadRequestException(`Missing answers for questions: ${missingQuestions.join(', ')}`);
    }

    if (extraQuestions.length > 0) {
      throw new BadRequestException(`Extra answers for questions not in exam: ${extraQuestions.join(', ')}`);
    }

    // Process question answers and calculate course-level answers
    const courseAnswers = new Map<number, { correct: number; wrong: number; unanswered: number }>();
    const questionAnswers: any[] = [];

    for (const question of questions) {
      const answer = createSessionDto.questionAnswers.find(qa => qa.questionId === question.id);
      const selectedAnswer = answer?.selectedAnswer || null;
      const isCorrect = selectedAnswer === question.correctAnswer;

      questionAnswers.push({
        questionId: question.id,
        questionNumber: question.questionNumber,
        courseId: question.courseId,
        selectedAnswer,
        isCorrect,
      });

      // Update course-level counters
      if (!courseAnswers.has(question.courseId)) {
        courseAnswers.set(question.courseId, { correct: 0, wrong: 0, unanswered: 0 });
      }

      const courseAnswer = courseAnswers.get(question.courseId)!;
      if (selectedAnswer === null) {
        courseAnswer.unanswered++;
      } else if (isCorrect) {
        courseAnswer.correct++;
      } else {
        courseAnswer.wrong++;
      }
    }

    // Convert to SessionAnswer format for scoring
    const sessionAnswers = Array.from(courseAnswers.entries()).map(([courseId, counts]) => ({
      courseId,
      correctAnswers: counts.correct,
      wrongAnswers: counts.wrong,
      unanswered: counts.unanswered,
    }));

    // Calculate scores using existing scoring service
    const { scoreDetails, totalScore } = await this.scoringService.calculateScores(
      exam,
      sessionAnswers,
    );

    // Add question-level answers to score details
    scoreDetails.questionAnswers = questionAnswers;

    // Create and save session
    const session = this.sessionRepository.create({
      examId: createSessionDto.examId,
      userId,
      totalScore,
      scoreDetails,
    });

    return this.sessionRepository.save(session);
  }

  async findAll(): Promise<Session[]> {
    return this.sessionRepository.find({
      relations: ['exam', 'user'],
    });
  }

  async findByUser(userId: number): Promise<Session[]> {
    return this.sessionRepository.find({
      where: { userId },
      relations: ['exam', 'user'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByExam(examId: number): Promise<Session[]> {
    return this.sessionRepository.find({
      where: { examId },
      relations: ['exam', 'user'],
      order: { totalScore: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Session> {
    const session = await this.sessionRepository.findOne({
      where: { id },
      relations: ['exam', 'user'],
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    // Calculate rank
    const allSessionsForExam = await this.sessionRepository.find({
      where: { examId: session.examId },
      order: { totalScore: 'DESC' },
    });

    let rank = 1;
    let previousScore = -1;
    let tieCount = 0;

    for (let i = 0; i < allSessionsForExam.length; i++) {
      const currentSession = allSessionsForExam[i];
      if (currentSession.totalScore !== previousScore) {
        rank = i + 1;
        tieCount = 0;
      } else {
        tieCount++;
      }
      if (currentSession.id === session.id) {
        session.rank = rank;
        break;
      }
      previousScore = currentSession.totalScore;
    }

    return session;
  }

  async recalculateSession(sessionId: number): Promise<Session> {
    // Find the session with its exam and user
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId },
      relations: ['exam', 'user'],
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${sessionId} not found`);
    }

    // Get the exam with courses
    const exam = await this.examRepository.findOne({
      where: { id: session.examId },
    });

    if (!exam) {
      throw new NotFoundException(`Exam with ID ${session.examId} not found`);
    }

    // Recalculate scores using existing answers
    const { scoreDetails, totalScore } = await this.scoringService.calculateScores(
      exam,
      session.scoreDetails.answers,
    );

    // Update the session with new scores
    session.totalScore = totalScore;
    session.scoreDetails = scoreDetails;

    return this.sessionRepository.save(session);
  }

  async getExamStatistics(examId: number): Promise<any> {
    const sessions = await this.findByExam(examId);

    if (sessions.length === 0) {
      return {
        examId,
        totalSessions: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        scoreDistribution: [],
      };
    }

    const scores = sessions.map(s => s.totalScore);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const highestScore = Math.max(...scores);
    const lowestScore = Math.min(...scores);

    // Create score distribution (0-10, 10-20, ..., 90-100)
    const scoreRanges = Array.from({ length: 10 }, (_, i) => ({
      range: `${i * 10}-${(i + 1) * 10}`,
      count: 0,
    }));

    scores.forEach(score => {
      // Ensure score is within valid range and calculate proper index
      const clampedScore = Math.max(0, Math.min(100, score));
      const rangeIndex = Math.min(Math.floor(clampedScore / 10), 9);
      if (rangeIndex >= 0 && rangeIndex < scoreRanges.length) {
        scoreRanges[rangeIndex].count++;
      }
    });

    return {
      examId,
      totalSessions: sessions.length,
      averageScore: Math.round(averageScore * 100) / 100,
      highestScore,
      lowestScore,
      scoreDistribution: scoreRanges,
    };
  }
}
