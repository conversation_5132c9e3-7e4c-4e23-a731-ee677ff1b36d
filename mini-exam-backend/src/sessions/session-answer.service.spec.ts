import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SessionsService } from './sessions.service';
import { Session } from '../entities/session.entity';
import { SessionAnswer } from '../entities/session-answer.entity';
import { Exam } from '../entities/exam.entity';
import { User } from '../entities/user.entity';
import { Question, AnswerOption } from '../entities/question.entity';
import { ScoringService } from '../scoring/scoring.service';

describe('SessionAnswer Integration', () => {
  let service: SessionsService;
  let sessionRepository: Repository<Session>;
  let sessionAnswerRepository: Repository<SessionAnswer>;

  const mockSessionRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockSessionAnswerRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockExamRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockQuestionRepository = {
    find: jest.fn(),
  };

  const mockScoringService = {
    calculateScores: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionsService,
        {
          provide: getRepositoryToken(Session),
          useValue: mockSessionRepository,
        },
        {
          provide: getRepositoryToken(SessionAnswer),
          useValue: mockSessionAnswerRepository,
        },
        {
          provide: getRepositoryToken(Exam),
          useValue: mockExamRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(Question),
          useValue: mockQuestionRepository,
        },
        {
          provide: ScoringService,
          useValue: mockScoringService,
        },
      ],
    }).compile();

    service = module.get<SessionsService>(SessionsService);
    sessionRepository = module.get<Repository<Session>>(getRepositoryToken(Session));
    sessionAnswerRepository = module.get<Repository<SessionAnswer>>(getRepositoryToken(SessionAnswer));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create session answers when creating a session by questions', async () => {
    // Mock data
    const mockExam = {
      id: 1,
      name: 'Test Exam',
      courses: [{ courseId: 1, questionCount: 2 }],
      questions: [],
    };

    const mockUser = {
      id: 1,
      username: 'testuser',
    };

    const mockQuestions = [
      {
        id: 1,
        examId: 1,
        courseId: 1,
        questionNumber: 1,
        correctAnswer: AnswerOption.A,
        course: { id: 1, name: 'Math' },
      },
      {
        id: 2,
        examId: 1,
        courseId: 1,
        questionNumber: 2,
        correctAnswer: AnswerOption.B,
        course: { id: 1, name: 'Math' },
      },
    ];

    const mockSession = {
      id: 1,
      examId: 1,
      userId: 1,
      totalScore: 50,
      scoreDetails: {},
    };

    const createSessionDto = {
      examId: 1,
      questionAnswers: [
        { questionId: 1, selectedAnswer: 'A' },
        { questionId: 2, selectedAnswer: 'C' },
      ],
    };

    // Setup mocks
    mockExamRepository.findOne.mockResolvedValue(mockExam);
    mockUserRepository.findOne.mockResolvedValue(mockUser);
    mockQuestionRepository.find.mockResolvedValue(mockQuestions);
    mockScoringService.calculateScores.mockResolvedValue({
      scoreDetails: { answers: [], courseScores: [], courseGroupScores: [] },
      totalScore: 50,
    });
    mockSessionRepository.create.mockReturnValue(mockSession);
    mockSessionRepository.save.mockResolvedValue(mockSession);
    mockSessionAnswerRepository.create.mockImplementation((data) => data);
    mockSessionAnswerRepository.save.mockResolvedValue([]);

    // Execute
    await service.createByQuestions(createSessionDto, 1);

    // Verify session answers were created
    expect(mockSessionAnswerRepository.create).toHaveBeenCalledTimes(2);
    expect(mockSessionAnswerRepository.create).toHaveBeenCalledWith({
      sessionId: 1,
      questionId: 1,
      userAnswer: 'A',
    });
    expect(mockSessionAnswerRepository.create).toHaveBeenCalledWith({
      sessionId: 1,
      questionId: 2,
      userAnswer: 'C',
    });
    expect(mockSessionAnswerRepository.save).toHaveBeenCalledTimes(1);
  });

  it('should retrieve session answers', async () => {
    const mockSessionAnswers = [
      {
        id: 1,
        sessionId: 1,
        questionId: 1,
        userAnswer: AnswerOption.A,
        question: { id: 1, questionText: 'What is 2+2?' },
      },
      {
        id: 2,
        sessionId: 1,
        questionId: 2,
        userAnswer: AnswerOption.C,
        question: { id: 2, questionText: 'What is 3+3?' },
      },
    ];

    mockSessionAnswerRepository.find.mockResolvedValue(mockSessionAnswers);

    const result = await service.getSessionAnswers(1);

    expect(result).toEqual(mockSessionAnswers);
    expect(mockSessionAnswerRepository.find).toHaveBeenCalledWith({
      where: { sessionId: 1 },
      relations: ['question', 'question.course'],
      order: { questionId: 'ASC' },
    });
  });
});
