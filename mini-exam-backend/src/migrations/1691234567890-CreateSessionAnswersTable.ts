import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSessionAnswersTable1691234567890 implements MigrationInterface {
  name = 'CreateSessionAnswersTable1691234567890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the session_answers table
    await queryRunner.createTable(
      new Table({
        name: 'session_answers',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'session_id',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'question_id',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'user_answer',
            type: 'enum',
            enum: ['A', 'B', 'C', 'D'],
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create unique composite index on session_id and question_id
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_session_answers_session_question"
      ON "session_answers" ("session_id", "question_id")
    `);

    // Create foreign key constraint for session_id
    await queryRunner.query(`
      ALTER TABLE "session_answers"
      ADD CONSTRAINT "FK_session_answers_session_id"
      FOREIGN KEY ("session_id") REFERENCES "sessions"("id") ON DELETE CASCADE
    `);

    // Create foreign key constraint for question_id
    await queryRunner.query(`
      ALTER TABLE "session_answers"
      ADD CONSTRAINT "FK_session_answers_question_id"
      FOREIGN KEY ("question_id") REFERENCES "questions"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "session_answers" DROP CONSTRAINT "FK_session_answers_question_id"`);
    await queryRunner.query(`ALTER TABLE "session_answers" DROP CONSTRAINT "FK_session_answers_session_id"`);

    // Drop the unique index
    await queryRunner.query(`DROP INDEX "IDX_session_answers_session_question"`);

    // Drop the table
    await queryRunner.dropTable('session_answers');
  }
}
