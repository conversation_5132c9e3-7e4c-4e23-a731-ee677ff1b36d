import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Course } from '../entities/course.entity';
import { CourseGroup } from '../entities/course-group.entity';
import { Exam, ExamCourse } from '../entities/exam.entity';
import {
  SessionCourseAnswer,
  CourseScore,
  CourseGroupScore,
  ScoreDetails,
} from '../entities/session.entity';

@Injectable()
export class ScoringService {
  constructor(
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(CourseGroup)
    private courseGroupRepository: Repository<CourseGroup>,
  ) {}

  /**
   * Calculate course percentage
   * Formula: (correct * positive_coeff - wrong * negative_coeff) / (total_questions * positive_coeff) * 100
   * Note: Scores can be negative (important for exam systems like Konkour)
   */
  private calculateCoursePercentage(
    correct: number,
    wrong: number,
    totalQuestions: number,
    positiveCoeff: number,
    negativeCoeff: number,
  ): number {
    const numerator = correct * positiveCoeff - wrong * negativeCoeff;
    const denominator = totalQuestions * positiveCoeff;
    if (denominator === 0) return 0;

    const percentage = (numerator / denominator) * 100;
    return Math.round(percentage * 100) / 100; // Round to 2 decimals, allow negative scores
  }

  /**
   * Calculate course group average
   * Formula: Average of course percentages in the group
   */
  private calculateCourseGroupAverage(courseScores: CourseScore[]): number {
    if (courseScores.length === 0) return 0;

    const sum = courseScores.reduce((acc, score) => acc + score.percentage, 0);
    return sum / courseScores.length;
  }

  /**
   * Calculate weighted course group score
   * Formula: group_average * group_positive_coefficient
   */
  private calculateWeightedCourseGroupScore(
    groupAverage: number,
    courseGroup: CourseGroup,
  ): number {
    if (!courseGroup) return 0;
    console.log(
      groupAverage,
      courseGroup.positiveCoefficient,
      groupAverage * courseGroup.positiveCoefficient,
    );
    return (
      Math.round(groupAverage * courseGroup.positiveCoefficient * 100) / 100
    );
  }

  /**
   * Calculate total score
   * Formula: Average of weighted course group scores
   */
  private calculateTotalScore(courseGroupScores: CourseGroupScore[]): number {
    if (courseGroupScores.length === 0) return 0;

    const sum = courseGroupScores.reduce(
      (acc, score) => acc + score.weightedScore,
      0,
    );
    return Math.round((sum / courseGroupScores.length) * 100) / 100;
  }

  async calculateScores(
    exam: Exam,
    answers: SessionCourseAnswer[],
  ): Promise<{ scoreDetails: ScoreDetails; totalScore: number }> {
    // Get course details
    const courseIds = exam.courses.map((c) => c.courseId);
    const courses = await this.courseRepository.find({
      where: { id: In(courseIds) },
      relations: ['courseGroup'],
    });

    // Calculate course scores
    const courseScores: CourseScore[] = [];

    for (const examCourse of exam.courses) {
      const course = courses.find((c) => c.id === examCourse.courseId);
      const answer = answers.find((a) => a.courseId === examCourse.courseId);

      if (!course || !answer) continue;

      const percentage = this.calculateCoursePercentage(
        answer.correctAnswers,
        answer.wrongAnswers,
        examCourse.questionCount,
        course.positiveCoefficient,
        course.negativeCoefficient,
      );

      console.log(
        percentage,
        'correctAnswers:' + answer.correctAnswers,
        'wrongAnswers:' + answer.wrongAnswers,
        'questionCount:' + examCourse.questionCount,
        'positiveCoefficient:' + course.positiveCoefficient,
        'negativeCoefficient:' + course.negativeCoefficient,
      );

      courseScores.push({
        courseId: course.id,
        courseName: course.name,
        percentage,
      });
    }

    // Group courses by course group
    const courseGroupMap = new Map<
      number,
      { courseGroup: CourseGroup; courses: Course[]; scores: CourseScore[] }
    >();

    for (const course of courses) {
      const groupId = course.courseGroup.id;
      if (!courseGroupMap.has(groupId)) {
        courseGroupMap.set(groupId, {
          courseGroup: course.courseGroup,
          courses: [],
          scores: [],
        });
      }

      const groupData = courseGroupMap.get(groupId)!;
      groupData.courses.push(course);

      const courseScore = courseScores.find((s) => s.courseId === course.id);
      if (courseScore) {
        groupData.scores.push(courseScore);
      }
    }

    // Calculate course group scores
    const courseGroupScores: CourseGroupScore[] = [];

    for (const [groupId, groupData] of courseGroupMap) {
      const averageScore = this.calculateCourseGroupAverage(groupData.scores);
      console.log('>>>>>>>>>>>>>>>', averageScore);
      const weightedScore = this.calculateWeightedCourseGroupScore(
        averageScore,
        groupData.courseGroup,
      );

      courseGroupScores.push({
        courseGroupId: groupId,
        courseGroupName: groupData.courseGroup.name,
        averageScore,
        weightedScore,
      });
    }

    // Calculate total score
    const totalScore = this.calculateTotalScore(courseGroupScores);

    const scoreDetails: ScoreDetails = {
      answers,
      courseScores,
      courseGroupScores,
    };

    return { scoreDetails, totalScore };
  }
}
