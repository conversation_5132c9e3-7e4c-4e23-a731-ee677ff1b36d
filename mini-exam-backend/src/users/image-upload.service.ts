import { Injectable, BadRequestException } from '@nestjs/common';
import * as sharp from 'sharp';
import * as fs from 'fs';
import * as path from 'path';

export interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
  processedImagePath?: string;
}

export interface ImageDimensions {
  width: number;
  height: number;
}

@Injectable()
export class ImageUploadService {
  private readonly uploadDir = path.join(process.cwd(), 'uploads', 'profile-images');
  private readonly maxFileSize = 70 * 1024; // 70KB
  private readonly minWidth = 200;
  private readonly minHeight = 300;
  private readonly maxWidth = 300;
  private readonly maxHeight = 400;
  private readonly validAspectRatios = [3/4, 4/6]; // 3:4 and 4:6 (which is 2:3)

  constructor() {
    // Ensure upload directory exists
    this.ensureUploadDirectory();
  }

  private ensureUploadDirectory(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  private calculateAspectRatio(width: number, height: number): number {
    return width / height;
  }

  private isValidAspectRatio(width: number, height: number): boolean {
    const aspectRatio = this.calculateAspectRatio(width, height);
    const tolerance = 0.05; // 5% tolerance for aspect ratio
    
    return this.validAspectRatios.some(validRatio => 
      Math.abs(aspectRatio - validRatio) <= tolerance
    );
  }

  private getTargetDimensions(width: number, height: number): ImageDimensions {
    const aspectRatio = this.calculateAspectRatio(width, height);
    
    // Determine if it's closer to 3:4 or 4:6 (2:3)
    const ratio34 = 3/4;
    const ratio46 = 4/6;
    
    const diff34 = Math.abs(aspectRatio - ratio34);
    const diff46 = Math.abs(aspectRatio - ratio46);
    
    if (diff34 <= diff46) {
      // Closer to 3:4 ratio
      if (width < this.minWidth || height < this.minHeight) {
        return { width: this.minWidth, height: this.minHeight };
      } else if (width > this.maxWidth || height > this.maxHeight) {
        return { width: this.maxWidth, height: this.maxHeight };
      }
    } else {
      // Closer to 4:6 (2:3) ratio
      const targetWidth = Math.min(this.maxWidth, Math.max(this.minWidth, width));
      const targetHeight = Math.round(targetWidth * (6/4)); // 6:4 = 3:2 inverted
      
      if (targetHeight > this.maxHeight) {
        const adjustedHeight = this.maxHeight;
        const adjustedWidth = Math.round(adjustedHeight * (4/6));
        return { width: adjustedWidth, height: adjustedHeight };
      }
      
      return { width: targetWidth, height: targetHeight };
    }
    
    return { width, height };
  }

  async processAndValidateImage(
    buffer: Buffer,
    originalName: string,
    userId: number
  ): Promise<ImageValidationResult> {
    const errors: string[] = [];

    try {
      // Get image metadata
      const image = sharp(buffer);
      const metadata = await image.metadata();

      if (!metadata.width || !metadata.height) {
        errors.push('Unable to determine image dimensions');
        return { isValid: false, errors };
      }

      // Check aspect ratio
      // if (!this.isValidAspectRatio(metadata.width, metadata.height)) {
      //   errors.push('Image must have aspect ratio of 3:4 or 4:6 (portrait orientation)');
      //   return { isValid: false, errors };
      // }

      // Determine target dimensions
      const targetDimensions = this.getTargetDimensions(metadata.width, metadata.height);

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `user_${userId}_${timestamp}.jpg`;
      const outputPath = path.join(this.uploadDir, filename);

      // Process image: resize, convert to JPEG, and compress
      let processedImage = image
        .resize(targetDimensions.width, targetDimensions.height, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({
          quality: 85,
          progressive: true
        });

      // Save the processed image
      await processedImage.toFile(outputPath);

      // Check final file size
      const stats = fs.statSync(outputPath);
      if (stats.size > this.maxFileSize) {
        // If still too large, reduce quality further
        let quality = 75;
        while (stats.size > this.maxFileSize && quality > 30) {
          processedImage = sharp(buffer)
            .resize(targetDimensions.width, targetDimensions.height, {
              fit: 'cover',
              position: 'center'
            })
            .jpeg({
              quality,
              progressive: true
            });

          await processedImage.toFile(outputPath);
          const newStats = fs.statSync(outputPath);
          
          if (newStats.size <= this.maxFileSize) {
            break;
          }
          
          quality -= 10;
        }

        // Final check
        const finalStats = fs.statSync(outputPath);
        if (finalStats.size > this.maxFileSize) {
          fs.unlinkSync(outputPath); // Clean up
          errors.push(`Unable to compress image to required size (${this.maxFileSize / 1024}KB)`);
          return { isValid: false, errors };
        }
      }

      return {
        isValid: true,
        errors: [],
        processedImagePath: filename
      };

    } catch (error) {
      errors.push(`Image processing failed: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  async deleteImage(filename: string): Promise<void> {
    if (!filename) return;
    
    const imagePath = path.join(this.uploadDir, filename);
    if (fs.existsSync(imagePath)) {
      fs.unlinkSync(imagePath);
    }
  }

  getImagePath(filename: string): string {
    return path.join(this.uploadDir, filename);
  }

  getImageUrl(filename: string): string {
    return `/uploads/profile-images/${filename}`;
  }
}
