import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { IsString, IsNumber, IsOptional, IsEnum } from 'class-validator';
import { Session } from './session.entity';
import { Question, AnswerOption } from './question.entity';

@Entity('session_answers')
@Index(['sessionId', 'questionId'], { unique: true }) // Composite unique constraint
export class SessionAnswer {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'session_id' })
  @IsNumber()
  sessionId: number;

  @ManyToOne(() => Session, session => session.sessionAnswers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'session_id' })
  session: Session;

  @Column({ name: 'question_id' })
  @IsNumber()
  questionId: number;

  @ManyToOne(() => Question, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'question_id' })
  question: Question;

  @Column({ 
    type: 'enum',
    enum: AnswerOption,
    name: 'user_answer',
    nullable: true 
  })
  @IsOptional()
  @IsEnum(AnswerOption)
  userAnswer: AnswerOption | null; // 'A', 'B', 'C', 'D', or null for unanswered

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
