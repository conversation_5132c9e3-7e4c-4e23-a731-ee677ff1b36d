import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON>olumn, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNumber, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { User } from './user.entity';
import { Exam } from './exam.entity';
import { SessionAnswer } from './session-answer.entity';

export class SessionCourseAnswer {
  courseId: number;
  correctAnswers: number;
  wrongAnswers: number;
  unanswered: number;
}

export class QuestionAnswer {
  questionId: number;
  questionNumber: number;
  courseId: number;
  selectedAnswer: string | null; // 'A', 'B', 'C', 'D', or null for unanswered
  isCorrect: boolean;
}

export class CourseScore {
  courseId: number;
  courseName: string;
  percentage: number;
}

export class CourseGroupScore {
  courseGroupId: number;
  courseGroupName: string;
  averageScore: number;
  weightedScore: number;
}

export class ScoreDetails {
  @ValidateNested({ each: true })
  @Type(() => SessionCourseAnswer)
  answers: SessionCourseAnswer[];

  @ValidateNested({ each: true })
  @Type(() => QuestionAnswer)
  questionAnswers?: QuestionAnswer[];

  @ValidateNested({ each: true })
  @Type(() => CourseScore)
  courseScores: CourseScore[];

  @ValidateNested({ each: true })
  @Type(() => CourseGroupScore)
  courseGroupScores: CourseGroupScore[];
}

@Entity('sessions')
export class Session {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'exam_id' })
  examId: number;

  @ManyToOne(() => Exam, exam => exam.sessions)
  @JoinColumn({ name: 'exam_id' })
  exam: Exam;

  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, user => user.sessions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'total_score' })
  @IsNumber()
  totalScore: number;

  @Column({ type: 'jsonb', name: 'score_details' })
  @IsObject()
  @ValidateNested()
  @Type(() => ScoreDetails)
  scoreDetails: ScoreDetails;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'int', nullable: true })
  rank?: number;

  @OneToMany(() => SessionAnswer, sessionAnswer => sessionAnswer.session)
  sessionAnswers: SessionAnswer[];
}
