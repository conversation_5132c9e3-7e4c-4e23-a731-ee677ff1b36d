# Session Answers Table

## Overview

The `session_answers` table has been created to store individual answers for each exam session. This provides a normalized approach to tracking student responses at the question level, enabling detailed analysis of student performance and answer patterns.

## Table Structure

### Table Name: `session_answers`

### Fields:

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| `id` | integer | Primary key (auto-increment) | PRIMARY KEY |
| `session_id` | integer | Foreign key referencing the exam session | NOT NULL, FK to sessions.id |
| `question_id` | integer | Foreign key referencing the specific question | NOT NULL, FK to questions.id |
| `user_answer` | enum('A','B','C','D') | The student's selected answer | NULLABLE (null for unanswered) |
| `createdAt` | timestamp | When the answer was first recorded | NOT NULL, DEFAULT now() |
| `updatedAt` | timestamp | When the answer was last modified | NOT NULL, DEFAULT now() |

### Constraints:

- **Composite Unique Index**: `(session_id, question_id)` - Ensures one answer per question per session
- **Foreign Key Constraints**: 
  - `session_id` → `sessions.id` (CASCADE DELETE)
  - `question_id` → `questions.id` (CASCADE DELETE)

## Entity Definition

The `SessionAnswer` entity is defined in `src/entities/session-answer.entity.ts`:

```typescript
@Entity('session_answers')
@Index(['sessionId', 'questionId'], { unique: true })
export class SessionAnswer {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'session_id' })
  sessionId: number;

  @ManyToOne(() => Session, session => session.sessionAnswers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'session_id' })
  session: Session;

  @Column({ name: 'question_id' })
  questionId: number;

  @ManyToOne(() => Question, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'question_id' })
  question: Question;

  @Column({ 
    type: 'enum',
    enum: AnswerOption,
    name: 'user_answer',
    nullable: true 
  })
  userAnswer: AnswerOption | null; // 'A', 'B', 'C', 'D', or null for unanswered

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## Usage

### Creating Session Answers

Session answers are automatically created when using the `createByQuestions` method in the `SessionsService`:

```typescript
const createSessionDto = {
  examId: 1,
  questionAnswers: [
    { questionId: 1, selectedAnswer: 'A' },
    { questionId: 2, selectedAnswer: 'C' },
    { questionId: 3, selectedAnswer: null }, // unanswered
  ],
};

const session = await sessionsService.createByQuestions(createSessionDto, userId);
```

### Retrieving Session Answers

Use the `getSessionAnswers` method to retrieve all answers for a specific session:

```typescript
const sessionAnswers = await sessionsService.getSessionAnswers(sessionId);
```

This returns an array of `SessionAnswer` entities with related question and course information.

### Querying Session Answers

You can also query session answers directly using the repository:

```typescript
// Get answers for a specific session
const answers = await sessionAnswerRepository.find({
  where: { sessionId: 1 },
  relations: ['question', 'question.course'],
  order: { questionId: 'ASC' },
});

// Get all correct answers for a session
const correctAnswers = await sessionAnswerRepository
  .createQueryBuilder('sa')
  .innerJoin('sa.question', 'q')
  .where('sa.sessionId = :sessionId', { sessionId: 1 })
  .andWhere('sa.userAnswer = q.correctAnswer')
  .getMany();

// Get unanswered questions for a session
const unanswered = await sessionAnswerRepository.find({
  where: { 
    sessionId: 1,
    userAnswer: IsNull(),
  },
  relations: ['question'],
});
```

## Benefits

1. **Detailed Analysis**: Track individual question performance across sessions
2. **Data Integrity**: Normalized structure with proper foreign key constraints
3. **Flexibility**: Easy to query for specific answer patterns or statistics
4. **Audit Trail**: Timestamps track when answers were recorded/modified
5. **Performance**: Indexed for efficient querying by session and question

## Migration

The table is created automatically when the application starts in development mode (due to `synchronize: true`). For production deployments, use the migration file:

```bash
# Run the migration (when TypeORM CLI is properly configured)
npm run typeorm:migration:run
```

The migration file is located at: `src/migrations/1691234567890-CreateSessionAnswersTable.ts`

## Backward Compatibility

The existing JSON-based approach in the `sessions.score_details` field is maintained for backward compatibility. Both approaches can coexist, with the normalized table providing additional querying capabilities.
