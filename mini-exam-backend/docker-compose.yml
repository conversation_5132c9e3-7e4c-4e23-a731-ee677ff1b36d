version: '3.8'

services:
  postgres:
    image: postgres:17.4
    container_name: min_exam_postgres_db
    restart: always
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  # db:
  #   image: mariadb:10.4.17
  #   container_name: min_exam_db
  #   # command: --default-authentication-plugin=mysql_native_password
  #   command: [
  #          '--default_authentication_plugin=mysql_native_password',
  #          '--character-set-server=utf8mb4',
  #          '--collation-server=utf8mb4_unicode_ci',
  #          '--sql-mode=ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'
  #      ]
  #   volumes:
  #     - .docker/db/mysql:/var/lib/mysql
  #    # - .docker/db:/config/databases
  #     - .docker/db/mysql/config:/etc/mysql/
  #     - .docker/.bash_history:/root/.bash_history
  #   #restart: always
  #   networks:
  #     - app-network
  #   ports:
  #     - 3306:3306
  #   environment:
  #     MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
  #     DB_HOST: ${DB_HOST}
  #     MYSQL_DATABASE: ${DB_DATABASE}
  #     MYSQL_USER: ${DB_USERNAME}
  #     MYSQL_PASSWORD: ${DB_PASSWORD}
  #     MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
  
  adminer:
    image: adminer
    restart: always
    ports:
      - 8082:8080
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # pgadmin:
  #   image: dpage/pgadmin4
  #   container_name: pgadmin4_container
  #   restart: always
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
  #     PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
  #   ports:
  #     - "5050:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  pgadmin_data:
  mongo_data:
